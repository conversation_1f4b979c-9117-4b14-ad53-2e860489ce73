# proyectoCRM

*Converted from: proyectoCRM.docx*

---

## 📊 File Information

- **File Name**: proyectoCRM.docx
- **File Size**: 15.03 KB
- **Modified Date**: 7/20/2025, 9:31:27 PM

## ⚠️ Conversion Warnings

- Unrecognised paragraph style: 'Body Text' (Style ID: BodyText)
- Unrecognised paragraph style: 'Contenido de la tabla (user)' (Style ID: Contenidodelatablauser)

## Content

##### APP CRM PERSONALIZADO

CONTEXTO

La empresa solicitante de la solución está trabajando en una prestación de servicios de preparación de documentos y asesoría a profesores de universidad que optan a promocionar.

Por otro lado, no hay una aplicación de gestión de seguimiento del cliente \(CRM\) con las necesidades particulares de este tipo de gestiones. En realidad se trata de incorporar en parte una aplicación de tipo empresarial \(ERP\) con un módulo de seguimiento del servicio en particular.

Por ello, se requiere una solución que gestione los clientes, además de un seguimiento de su estado de servicio en el día a día.

REQUISITOS

DESCRIPCIÓN GENERAL DE LA SOLUCIÓN:

RECORRIDO CLIENTE

El cliente contacta con la empresa, por teléfono, whastapp o mail. Se habla con él y se le envía un presupuesto en el que va incluida la fecha de inicio y finalización.

Puede aceptarlo o no.

Si lo acepta, se hace un anticipo.

Y se queda para comenzar a trabajar en la fecha indicada en el presupuesto.

Cuando se finaliza el trabajo se hace la factura final y el cliente paga la cantidad restante, que puede ser realizada en uno o varios plazos.

Hay clientes que no aceptan el presupuesto.

Otros que lo aceptan pero por alguna razón se empieza a trabajar más adelante.

CASOS DE ESTUDIO

Se aclara que cuando hay una acción, salvo que se indique lo contrario, será mediante formulario.

CASO DE ESTUDIO 1:

1. El __cliente__ contacta con la empresa.
	1. En ese momento, se  registra mediante un formulario la información del __cliente.__
	2. Además, se abre un formulario para añadir sobre el cliente un registro de __comunicación__, donde se almacena, entre otros datos, la fecha de comunicación, y el medio de la misma, que puede ser *teléfono*, *whastapp* o *mail*. Además, se abre una __gestión de expediente__ sobre el mencionado cliente, con información sobre el servicio a otorgar.
2. Se habla con él y se le envía un __presupuesto__ por e-mail \(mediante un botón que se acciona desde un formulario, con sus correspondientes gestiones de errores\), dentro de la correspondiente gestión, en el que va incluida la fecha de inicio y fecha de finalización.

CASO DE ESTUDIO 1.1

1. Se lleva a cabo el caso de estudio 1.
2. El cliente NO acepta el __presupuesto__.
3. Se registra en el formulario de edición de la __gestión__ del __cliente__ el estado del __presupuesto__ *NO aceptado*.

CASO DE ESTUDIO 1.2:

1. Se lleva a cabo el __caso de estudio 1__.
2. El cliente SÍ acepta el __presupuesto__. Se pone su estado como *aceptado* en el correspondiente formulario.
3. En ese momento, la aplicación, de forma automática, hace que el __cliente__ pasa a ser __cliente contratado__. \(NOTA: Este proceso debe poder revertirse mediante acción manual posteriormente si se desea\).
	1. Con el __cliente contratado__, se crea una o varias __gestión de servicio__ \(de forma automática\). La __gestión de servicio__ tiene como particularidad, que tiene un registro de __pagos__ que realiza el __cliente contratado__. Se mantiene la __gestión del expediente__ del cliente. El __presupuesto__ sigue vinculado al cliente mediante la __gestión del expediente__, ya que __cliente-contratado__ es un subtipo heredado de __cliente__.
4. El __cliente contratado__ realiza, dentro de su __gestión de servicio__, un __pago__ por una cantidad, que es un porcentaje a determinar, en una fecha de pago, de tipo *anticipo*. Se actualiza en ese momento el estado de la __gestión de servicio__ como *pago anticipado realizado*. Este __pago__ puede hacerse en uno o varios plazos. Por tanto, dentro del __pago__, puede haber __pago único__ y __pago a plazos__:
	1. En el primer caso \(__pago único__\), no se indica el plazo.
	2. En __pago fraccionado__, es un tipo de __pago__ que incluye el . En ese __plazo__, se indica la fecha, cantidad, porcentaje del pago, forma de pago y estado de pago.
		1. La forma de pago puede ser en *efectivo*, *bizum* ó *transferencia*.
		2. El estado de pago puede ser *pendiente*, *ordenado por el cliente* o* pagado*.
5. En la __gestión del servicio__, se indica la fecha de inicio del proyecto, que puede ser inmediatamente tras la aceptación del presupuesto y pago anticipado \(día siguiente\), o en una fecha posterior. En el segundo caso, se debe indicar el la gestión del servicio, un registro de demora adjunto, en el que se indica el motivo por el que se demora el inicio del proyecto.
6. A lo largo de la realización del proyecto, puede haber __pagos__ \(sea único o a plazos\) intermedios. Por tanto, el __pago__ tendrá un campo momento que puede ser *anticipado*, *intermedio* o *final*.
7. Al finalizar el servicio, se genera la __factura__, donde se indica el importe completo final, servicios pendiente de pago, junto a la *fecha de emisión*, y datos del __cliente__, entre otros datos.
8. El __pago__ de momento *final* se realiza una vez realizado el proyecto. El importe es el total indicado en la factura, mencionada en el punto anterior, menos el importe entregado por el cliente en los pagos anteriores. Como el resto de pagos, puede ser __pago único__ o __pago a plazos__.

MODELO DE DOMINIOS

A raíz de los casos de uso expuestos anteriormente, se extraen los siguientes dominios:

- Dominio CLIENTES
	- Gestión de clientes \(clientes potenciales y clientes contratados\).
	- Gestión de comunicaciones con los clientes.
	- Modelo de datos:
		- Entidad CLIENTE.
			- Puede tener ninguno o muchos registros de tipo COMUNICACIÓN-CLIENTE.
			- Puede tener ninguno o muchos registros de tipo GESTIÓN-EXPEDIENTE.
			- Sus campos \(de cliente\) son:

ID

Autonumérico

PK

ID\_manual

Varchar\(50\)

Único + No-Nulo

NIFE

Varchar\(15\)

Único + No-Nulo

Nombre

Varchar\(255\)

No-Nulo

Apellidos

Varchat\(255\)

No-Nulo

Teléfono1

Varchar\(20\)

No-Nulo

Teléfono2

Varchar\(20\)

Teléfono3

Varchar\(20\)

Email1

Varchar\(255\)

No-Nulo

Email2

Varchar\(255\)

Email3

Varchar\(255\)

Expedientes

Array Gestión-Expediente

-
	-
		- Entidad CLIENTE-CONTRATADO \(subtipo heredado de CLIENTE\).
			- Puede tener ninguno o muchos registros mediante inclusión de tipo COMUNICACIÓN-CLIENTE-CONTRATADO.
			- Puede tener ninguno o muchos registros mediante inclusión de GESTIÓN-SERVICIO.
			- Puede tener ninguno o muchos registros mediante inclusión de FACTURA.
			- Datos:

Además de los de cliente, ya que es una clase heredada:

Comunicaciones

Array de Comunicación Cliente Contratado

Servicios

Array de Gestión-Servicio

-
	-
		- Entidad COMUNICACIÓN-CLIENTE.
			- Datos:

ID Comunicación

Autonumérico

PK

ID Cliente

Número

FK a Cliente.ID-Cliente - No-Nulo

ID Expediente

Número

FK a Gestión Expediente. Valorar si en este caso es mejor desarrollar una clase heredada.

Fecha-Hora Comunicación

Fecha-Hora

No-Nulo \(junto ID cliente en BD\)

Medio de comunicación

A elegir uno entre lista:
- e-mail
- Teléfono
- Mensajería instantánea

No-Nulo

Texto

Texto largo \(LONGTEXT\)

No-Nulo

Resumen

Texto \(TEXT\)

No-Nulo

-
	-
		- Entidad COMUNICACIÓN-CLIENTE-CONTRATADO \(subtipo heredado de COMUNICACIÓN-CLIENTE\).
Además de su clase padre, también puede tener una comunicación que esté ligada a una GESTIÓN-SERVICIO.
		- Datos:

Además de los de cliente, ya que es una clase heredada:

ID Servicio

Número

FK a Gestión Servicio. – No-Nulo. Valorar si en este caso es mejor desarrollar una clase heredada.

- Dominio GESTIÓN
	- Gestión de expedientes del cliente.
		- Gestión de presupuesto de los expedientes del cliente.
	- Gestión de servicios del cliente.
	- Modelo de datos:
		- Entidad GESTIÓN-EXPEDIENTE.
		- Puede tener ninguno o muchos registros mediante inclusión de PRESUPUESTOS. La idea de los presupuestos no es generarlo, si no tener un enlace al PDF. Otra cosa es que se pueda autorellenar mediante una IA para tener los datos más fácilmente accesibles mediante formularios.
			- Datos:

ID Expediente

Autonumérico

PK

ID Cliente

Número

FK a Cliente.ID-Cliente - No-Nulo

Fecha-Hora Creación

Fecha-Hora

No-Nulo \(junto ID cliente en BD\)

Resumen Expediente

Texto \(TEXT\)

No-Nulo

Texto

Texto largo \(LONGTEXT\)

\(Analizar si array de Comunicaciones de Gestión de Expedientes en lugar de que la Comunicación del Expediente apunte aquí.\)

Presupuestos

Array de Presupuestos

-
	-
		- Entidad GESTIÓN-SERVICIO.
			- Puede tener ninguno o muchos registros mediante inclusión de PAGOS.
			- Puede tener ninguno o muchos registros mediante inclusión de COMUNICACIÓN-CLIENTE-CONTRATADO \(indicado anteriormente\).
			- Debe poderse tener en cuenta los pagos ya realizados, y los pendientes, para calcular el total pagado del servicio, y el total pendiente del mismo.
			- Otros requisitos funcionales:
				- Debe poderse gestionar \(crear-modificar-eliminar\) desde el listado de clientes, o bien un listado general de servicios en el que se pueda filtrar por clientes.
				- Hay un sub-tipo de servicios, que son los Servicios Complementarios, que no entra dentro del catálogo \(no está en Tipo-Servicio\). Eso debe poderse gestionar desde el formulario de Comunicación del Cliente, y Comunicación de Cliente Contratado, ya que este tipo de servicios, suele darse a raíz de una comunicación rápida. Lo suyo es que se marque un checkbox, y automáticamente se genere, con el tipo “Complementario”, junto a sus entidades vinculadas correspondientes.

Si se realiza sobre la comunicación de un cliente no-contratado, al marcarse el checkbox, automáticamente se debe convertir a dicho cliente en contratado, ya que se le aplica un servicio.

-
	-
		-
			- Datos entidad GESTIÓN SERVICIO:

ID Servicio

Autonumérico

PK

ID Cliente Contratado

Número

FK a Cliente Contratado. – No-Nulo.

Fecha Creación Servicio

Fecha-Hora

No-Nulo

Fecha Inicio Servicio

Fecha-Hora

Fecha Prevista Finalización Servicio

Fecha-Hora

Tipo Servicio

A elegir uno entre lista:

- Acreditación Titular Universitario

- Acreditación Catedrático Universidad

- Acreditación Contratado Doctor

- Sexenio

- Gestión del Currículum

- Creación Página Web

- Actualización Página Web

- Gestión de Información

- Complementario

Comentario

Texto Medio

\(Analizar si array de Comunicaciones de Gestión de Servicios en lugar de que en Comunicaciones haya un atributo que apunte aquí.\)

Precio

Número Decimal

No-Nulo

Pagado

Número Decimal

No-Nulo, Autocalculado a partir de los pagos

Pendiente-Pago

Número Decimal

No-Nulo, Autocalculado a partir del precio total menos los pagos realizados

Pagos

Array de Pagos Realizados

-
	-
		- Entidad PRESUPUESTO
			- Se aplica sobre un expediente.
			- A partir de ahí, se genera los servicios correspondientes.
			- Datos entidad PRESUPUESTO

ID Presupuesto

Autonumérico

PK

ID Cliente Contratado

Número

FK a Cliente Contratado. – No-Nulo.

Fecha

Fecha-Hora

Tipos Servicios

A elegir uno o varios entre lista:

- Acreditación Titular Universitario

- Acreditación Catedrático Universidad

- Acreditación Contratado Doctor

- Sexenio

- Gestión del Currículum

- Creación Página Web

- Actualización Página Web

- Gestión de Información

- Complementario

Descripción-Concepto

Texto Medio

Precio

Número Decimal

Enlace PDF

Enlace

No-Nulo

Estado

A elegir uno entre la lista:

- Generado

- Enviado

- Aceptado

- Rechazado

\(A tener en cuenta que Fecha, Concepto, Descripción y Precio pueden ser nulos porque la info ya está en el PDF. POSIBILIDAD DE APLICAR MÓDULO QUE RECONOZCA EL PDF Y RELLENE ESTOS CAMPOS AUTOMÁTICAMENTE, POR EJEMPLO CONSULTANTDO UNA CUENTA CHAT-GPT\).

- Dominio FACTURAS
	- No se trata de generar facturas, si no de tener un repositorio para subir facturas  en PDF.
		- La idea de las facturas no es generarlas, si no tener un enlace al PDF. Otra cosa es que se pueda autorellenar mediante una IA para tener los datos más fácilmente accesibles mediante formularios.
	- Debe poderse subir facturas de los servicios prestados a un cliente contratado. Puede ser uno o varios servicios.
	- Modelo de datos:
		- FACTURAS

ID Factura

Autonumérico

PK

ID Cliente Contratado

Número

FK a Cliente.ID-Cliente Contratado – No-Nulo

Fecha

Fecha-Hora

Servicios

Array de Servicios sobre los que están las facturas de los clientes.

Comentario

Texto

Precio

Número Decimal

Enlace PDF

Enlace

No-Nulo

\(A tener en cuenta que Fecha, Servicios, Comentario y Precio pueden ser nulos porque la info ya está en el PDF. POSIBILIDAD DE APLICAR MÓDULO QUE RECONOZCA EL PDF Y RELLENE ESTOS CAMPOS AUTOMÁTICAMENTE, POR EJEMPLO CONSULTANTDO UNA CUENTA CHAT-GPT\).

- Dominio PAGOS
	- Gestión de pagos únicos
	- Gestión de pagos aplazados
	- Requisitos funcionales:
		- El pago sólo está realizado, cuando se marca explícitamente así en el formulario \(no hay ningún caso en el que se marque automáticamente como tal, ya que no hay terminales TPV ni Bizum, ni APIs con cuentas bancarias vinculadas\), o cuando se actualiza el estado del pago de un plazo, si se comprueba que todos los plazos del mismo están pagados, entonces, sí se debe marcar automáticamente como tal.
		- Un pago pertenece a una Gestión de Servicio
	- Modelo de datos:
		- Pago

ID Pago

Autonumérico

PK

ID Servicio

Número

FK a Servicio correspondiente

Tipo

A elegir uno entre lista:

- Pre-Servicio \(Antes de trabajar\)

- En-Servicio \(Durante el trabajo del servicio\)

- Post-Servicio \(Con el trabajo del Servicio ya realizado\)

No-Nulo

Modo Pago

A elegir uno entre lista:

- Bizum

- Transferencia

- Efectivo

No-Nulo

Comentario

Texto Medio

Cantidad

Número Decimal

No-Nulo

Estado

A elegir uno entre lista:

- Pendiente

- Ordenado

- Pagado

No-Nulo

Fecha Pagado

Fecha-Hora

Sólo cuando el estado es Pagado, si no, lo fuerza a NULO. Se pone automáticamente al poner estado pagado \(aunque se puede modificar manualmente\). Si es aplazado, comprueba que TODOS los plazos están en estado pagado. Si no, también se fuerza a NULO.

-
	-
		- Datos Pago aplazado

ID Plazo

Autonumérico

PK

ID Pago

Número

FK a Pago correspondiente

Plazo

Número

No-Nulo. No puede repetirse el número para un mismo ID Pago.

Cantidad

Número Decimal

No-Nulo

Estado

A elegir uno entre lista:

- Pendiente

- Ordenado

- Pagado

No-Nulo

Fecha Pagado

Fecha-Hora

Sólo cuando el estado es Pagado, si no, lo fuerza a NULO. Se pone automáticamente al poner estado pagado \(aunque se puede modificar manualmente\).

DETALLES TÉCNICOS:

- La solución será desarrollada en PHP 8.3.
- La base de datos será MariaDB.
- La parte de BACK-END, usar un framework, por ejemplo Symphony, o crear uno sencillo a mano para control de API, lo que consideres más adecuado.
- La parte perteneciente a FRONT-END será, preferiblemente, basado en AJAX, utilizando librerías JavaScript con librerías React. Si fuera necesario, se puede incluir JQuery, pero preferiblemente no utilizarlo.
- Aplicación Orientada a Objetos, usando buenas prácticas, principios SOLID, patrones de diseño software.
	- Como se ha indicado anteriormente, se realizará un enfoque de diseño orientado/dirigido a DOMINIOS \(DDD\).
	- Se usará tipado fuerte.